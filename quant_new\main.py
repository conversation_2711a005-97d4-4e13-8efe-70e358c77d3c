"""
量化回测引擎主入口

提供简单易用的接口来运行量化策略回测。
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置环境变量
os.environ['PYTHONPATH'] = str(current_dir)

try:
    from core.config.backtest import BacktestConfig
    from dataseed.mock import MockDataSeed
    from dataseed.akshare import AkShareDataSeed
    from strategies.single.macd import MACDStrategy, MACDConfig
    from strategies.single.rsi import RSIStrategy, RSIConfig
    from utils.logger import init_logger, get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在quant_new目录下运行此脚本")
    sys.exit(1)

# 初始化日志系统
init_logger()
logger = get_logger(__name__)


class QuantEngine:
    """量化回测引擎主类"""

    def __init__(self, data_source: str = "mock"):
        """
        初始化量化引擎

        Args:
            data_source: 数据源类型 ("mock", "akshare")
        """
        self.data_source_type = data_source
        self.data_source = self._create_data_source(data_source)

        logger.info(f"量化引擎初始化完成，数据源: {data_source}")

    def _create_data_source(self, source_type: str):
        """创建数据源"""
        if source_type == "mock":
            return MockDataSeed()
        elif source_type == "akshare":
            try:
                return AkShareDataSeed()
            except ImportError:
                logger.warning("AkShare未安装，使用Mock数据源")
                return MockDataSeed()
        else:
            raise ValueError(f"不支持的数据源类型: {source_type}")

    def run_strategy(
        self,
        strategy_name: str,
        symbol: str,
        start_date: str,
        end_date: str,
        strategy_params: Optional[Dict[str, Any]] = None,
        backtest_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        运行策略回测

        Args:
            strategy_name: 策略名称 ("macd", "rsi")
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            strategy_params: 策略参数
            backtest_params: 回测参数

        Returns:
            回测结果字典
        """
        try:
            # 获取数据
            logger.info(f"获取 {symbol} 数据: {start_date} 到 {end_date}")
            data = self.data_source.get_daily_data(symbol, start_date, end_date)

            if data.empty:
                raise ValueError(f"未获取到 {symbol} 的数据")

            logger.info(f"数据获取成功，共 {len(data)} 条记录")

            # 创建策略
            strategy = self._create_strategy(strategy_name, strategy_params or {})

            # 创建回测配置
            backtest_config = self._create_backtest_config(
                start_date, end_date, backtest_params or {}
            )

            # 运行策略
            logger.info(f"运行策略: {strategy_name}")
            result = strategy.run(data, backtest_config)

            # 转换结果为字典
            result_dict = result.to_dict()
            result_dict['data_points'] = len(data)
            result_dict['strategy_params'] = strategy_params
            result_dict['backtest_params'] = backtest_params

            logger.info(f"策略运行完成，总收益: {result.total_return:.2%}")

            return result_dict

        except Exception as e:
            logger.error(f"策略运行失败: {e}")
            raise

    def _create_strategy(self, strategy_name: str, params: Dict[str, Any]):
        """创建策略实例"""
        strategy_name = strategy_name.lower()

        if strategy_name == "macd":
            config = MACDConfig(**params)
            return MACDStrategy(config)
        elif strategy_name == "rsi":
            config = RSIConfig(**params)
            return RSIStrategy(config)
        else:
            raise ValueError(f"不支持的策略: {strategy_name}")

    def _create_backtest_config(
        self,
        start_date: str,
        end_date: str,
        params: Dict[str, Any]
    ) -> BacktestConfig:
        """创建回测配置"""
        default_params = {
            'start_date': start_date,
            'end_date': end_date,
            'initial_cash': 100000.0,
            'commission': 0.0005,
            'slippage': 0.001
        }

        # 合并参数
        config_params = {**default_params, **params}

        return BacktestConfig(**config_params)

    def get_available_symbols(self, market: Optional[str] = None) -> List[str]:
        """
        获取可用的股票列表

        Args:
            market: 市场代码 ("sh", "sz")

        Returns:
            股票代码列表
        """
        try:
            symbols = self.data_source.get_universe(market=market)
            logger.info(f"获取到 {len(symbols)} 个股票代码")
            return symbols
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_strategy_params(self, strategy_name: str) -> Dict[str, Any]:
        """
        获取策略参数说明

        Args:
            strategy_name: 策略名称

        Returns:
            参数说明字典
        """
        strategy_name = strategy_name.lower()

        if strategy_name == "macd":
            return {
                'description': MACDStrategy.get_param_description(),
                'constraints': MACDStrategy.get_param_constraints()
            }
        elif strategy_name == "rsi":
            return {
                'description': RSIStrategy.get_param_description(),
                'constraints': RSIStrategy.get_param_constraints()
            }
        else:
            raise ValueError(f"不支持的策略: {strategy_name}")

    def batch_test(
        self,
        strategy_name: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        strategy_params: Optional[Dict[str, Any]] = None,
        backtest_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量回测多个股票

        Args:
            strategy_name: 策略名称
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            strategy_params: 策略参数
            backtest_params: 回测参数

        Returns:
            {symbol: result} 字典
        """
        results = {}

        for symbol in symbols:
            try:
                logger.info(f"回测股票: {symbol}")
                result = self.run_strategy(
                    strategy_name=strategy_name,
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    strategy_params=strategy_params,
                    backtest_params=backtest_params
                )
                results[symbol] = result

            except Exception as e:
                logger.error(f"股票 {symbol} 回测失败: {e}")
                results[symbol] = {'error': str(e)}

        return results


def demo_single_strategy():
    """演示单个策略回测"""
    print("=== 单策略回测演示 ===")

    # 创建引擎
    engine = QuantEngine(data_source="mock")

    # MACD策略参数
    macd_params = {
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9,
        'signal_threshold': 0.3
    }

    # 回测参数
    backtest_params = {
        'initial_cash': 100000,
        'commission': 0.0005
    }

    # 运行回测
    result = engine.run_strategy(
        strategy_name="macd",
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-12-31",
        strategy_params=macd_params,
        backtest_params=backtest_params
    )

    # 打印结果
    print(f"策略: {result['strategy_name']}")
    print(f"股票: {result['symbol']}")
    print(f"总收益: {result['total_return']:.2%}")
    print(f"年化收益: {result['annual_return']:.2%}")
    print(f"夏普比率: {result['sharpe_ratio']:.2f}")
    print(f"最大回撤: {result['max_drawdown']:.2%}")
    print(f"胜率: {result['win_rate']:.2%}")


def demo_batch_test():
    """演示批量回测"""
    print("\n=== 批量回测演示 ===")

    # 创建引擎
    engine = QuantEngine(data_source="mock")

    # 获取股票列表
    symbols = engine.get_available_symbols()[:3]  # 只测试前3个

    # RSI策略参数
    rsi_params = {
        'rsi_period': 14,
        'oversold_threshold': 30,
        'overbought_threshold': 70,
        'signal_threshold': 0.4
    }

    # 批量回测
    results = engine.batch_test(
        strategy_name="rsi",
        symbols=symbols,
        start_date="2023-01-01",
        end_date="2023-12-31",
        strategy_params=rsi_params
    )

    # 打印结果
    print(f"回测股票数量: {len(symbols)}")
    for symbol, result in results.items():
        if 'error' in result:
            print(f"{symbol}: 失败 - {result['error']}")
        else:
            print(f"{symbol}: 收益 {result['total_return']:.2%}, 夏普 {result['sharpe_ratio']:.2f}")


def demo_strategy_params():
    """演示策略参数查询"""
    print("\n=== 策略参数查询演示 ===")

    engine = QuantEngine()

    # 查询MACD策略参数
    macd_info = engine.get_strategy_params("macd")
    print("MACD策略参数:")
    for param, desc in macd_info['description'].items():
        constraints = macd_info['constraints'].get(param, {})
        print(f"  {param}: {desc} {constraints}")

    print("\nRSI策略参数:")
    rsi_info = engine.get_strategy_params("rsi")
    for param, desc in rsi_info['description'].items():
        constraints = rsi_info['constraints'].get(param, {})
        print(f"  {param}: {desc} {constraints}")


if __name__ == "__main__":
    try:
        # 运行演示
        demo_single_strategy()
        demo_batch_test()
        demo_strategy_params()

        print("\n=== 演示完成 ===")

    except Exception as e:
        logger.error(f"演示运行失败: {e}")
        print(f"错误: {e}")
